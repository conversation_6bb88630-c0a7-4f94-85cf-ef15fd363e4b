#!/usr/bin/env python3
"""
Startup script for ATE MEG's FROZEN FOODS POS System
This script ensures the database is initialized and starts the Flask app
"""

import os
import sys
import subprocess

def check_database():
    """Check if database exists and has data"""
    if not os.path.exists('pos_system.db'):
        print("🔄 Database not found. Initializing...")
        return False
    
    # Check if database has products
    try:
        from app import app, Product
        with app.app_context():
            product_count = Product.query.count()
            if product_count == 0:
                print("🔄 Database empty. Adding sample data...")
                return False
            else:
                print(f"✅ Database ready with {product_count} products")
                return True
    except Exception as e:
        print(f"🔄 Database issue detected: {e}")
        return False

def initialize_database():
    """Initialize database with sample data"""
    try:
        print("📦 Installing dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        print("🗄️ Initializing database...")
        subprocess.run([sys.executable, "init_data.py"], check=True)
        
        print("📊 Adding sample sales data...")
        subprocess.run([sys.executable, "create_sample_sales.py"], check=True)
        
        print("✅ Database initialization complete!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during initialization: {e}")
        return False

def start_app():
    """Start the Flask application"""
    print("🚀 Starting ATE MEG's FROZEN FOODS POS System...")
    print("📱 The app will open automatically in your browser")
    print("🌐 Access URL: http://localhost:5000")
    print("=" * 50)
    
    # Import and run the app
    from app import app
    app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == "__main__":
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print("=" * 50)
    
    # Check if database is ready
    if not check_database():
        if not initialize_database():
            print("❌ Failed to initialize database. Exiting.")
            sys.exit(1)
    
    # Start the application
    start_app()
