
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone, date, timedelta
import os

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///frozen_foods_pos.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Ensure instance folder exists
if not os.path.exists('instance'):
    os.makedirs('instance')

db = SQLAlchemy(app)

# Add custom Jinja2 filters
@app.template_filter('stock')
def stock_filter(products):
    """Filter products by stock level"""
    return [p for p in products if p.stock > 0]

@app.template_filter('low_stock')
def low_stock_filter(products, threshold=5):
    """Filter products with low stock"""
    return [p for p in products if p.stock <= threshold]

@app.template_filter('out_of_stock')
def out_of_stock_filter(products):
    """Filter products that are out of stock"""
    return [p for p in products if p.stock == 0]

# Database Models
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False, default=0.0)  # Selling price
    stock = db.Column(db.Integer, nullable=False, default=0)
    received = db.Column(db.Integer, nullable=False, default=0)
    sold = db.Column(db.Integer, nullable=False, default=0)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    @property
    def capital_price(self):
        """Calculate capital price as 70% of selling price"""
        return round(self.price * 0.7, 2)

    @property
    def profit_per_unit(self):
        """Calculate profit per unit (selling price - capital price)"""
        return round(self.price - self.capital_price, 2)

    @property
    def total_profit(self):
        """Calculate total profit from sold units"""
        return round(self.sold * self.profit_per_unit, 2)

    @property
    def total_revenue(self):
        """Calculate total revenue from sold units"""
        return round(self.sold * self.price, 2)

    @property
    def total_capital_invested(self):
        """Calculate total capital invested in received stock"""
        return round(self.received * self.capital_price, 2)

    def __repr__(self):
        return f'<Product {self.name}>'

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    sale_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    product = db.relationship('Product', backref=db.backref('sales', lazy=True))

    def __repr__(self):
        return f'<Sale {self.product.name} - {self.quantity}>'

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    transaction_number = db.Column(db.String(20), unique=True, nullable=False)
    total_amount = db.Column(db.Float, nullable=False)
    transaction_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<Transaction {self.transaction_number}>'

class TransactionItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

    transaction = db.relationship('Transaction', backref=db.backref('items', lazy=True))
    product = db.relationship('Product', backref=db.backref('transaction_items', lazy=True))

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)
    total_credit = db.Column(db.Float, nullable=False, default=0.0)
    total_paid = db.Column(db.Float, nullable=False, default=0.0)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<Customer {self.name}>'

    @property
    def balance(self):
        return self.total_credit - self.total_paid

class CreditTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=True)
    amount = db.Column(db.Float, nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'credit' or 'payment'
    description = db.Column(db.Text, nullable=True)
    is_paid = db.Column(db.Boolean, nullable=False, default=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    customer = db.relationship('Customer', backref=db.backref('credit_transactions', lazy=True))
    transaction = db.relationship('Transaction', backref=db.backref('credit_transaction', uselist=False))

    def __repr__(self):
        return f'<CreditTransaction {self.customer.name} - {self.amount}>'

# Routes
@app.route('/')
def index():
    # Get dashboard statistics
    total_products = Product.query.count()
    total_sales = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
    low_stock_products = Product.query.filter(Product.stock <= 5).all()
    low_stock_count = len(low_stock_products)
    total_transactions = Transaction.query.count()

    return render_template('index.html',
                         total_products=total_products,
                         total_sales=total_sales,
                         low_stock_products=low_stock_products,
                         low_stock_count=low_stock_count,
                         total_transactions=total_transactions)

@app.route('/products')
def products():
    products = Product.query.all()
    return render_template('products.html', products=products)

@app.route('/add_product', methods=['GET', 'POST'])
def add_product():
    if request.method == 'POST':
        name = request.form['name']
        price = float(request.form.get('price', 0))
        received = int(request.form.get('received', 0))

        product = Product(name=name, price=price, received=received, stock=received)
        db.session.add(product)
        db.session.commit()
        flash('Product added successfully!', 'success')
        return redirect(url_for('products'))

    return render_template('add_product.html')

@app.route('/edit_product/<int:id>', methods=['GET', 'POST'])
def edit_product(id):
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        product.name = request.form['name']
        product.price = float(request.form.get('price', 0))
        old_received = product.received
        new_received = int(request.form.get('received', 0))

        # Update stock based on received quantity change
        stock_change = new_received - old_received
        product.received = new_received
        product.stock += stock_change
        product.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        flash('Product updated successfully!', 'success')
        return redirect(url_for('products'))

    return render_template('edit_product.html', product=product)

@app.route('/delete_product/<int:id>')
def delete_product(id):
    product = Product.query.get_or_404(id)
    db.session.delete(product)
    db.session.commit()
    flash('Product deleted successfully!', 'success')
    return redirect(url_for('products'))

@app.route('/pos')
def pos():
    products = Product.query.filter(Product.stock > 0).all()
    customers = Customer.query.all()
    return render_template('pos.html', products=products, customers=customers)

@app.route('/process_sale', methods=['POST'])
def process_sale():
    cart_items = request.json.get('cart_items', [])
    customer_id = request.json.get('customer_id')
    payment_method = request.json.get('payment_method', 'cash')

    if not cart_items:
        return jsonify({'success': False, 'message': 'Cart is empty'})

    # Validate customer for credit sales
    if payment_method == 'credit' and not customer_id:
        return jsonify({'success': False, 'message': 'Customer must be selected for credit sales'})

    # Generate transaction number
    transaction_number = f"TXN{datetime.now().strftime('%Y%m%d%H%M%S')}"

    # Calculate total
    total_amount = 0
    transaction_items = []

    for item in cart_items:
        product = Product.query.get(item['product_id'])
        if not product or product.stock < item['quantity']:
            return jsonify({'success': False, 'message': f'Insufficient stock for {product.name if product else "unknown product"}'})

        item_total = item['quantity'] * product.price
        total_amount += item_total

        transaction_items.append({
            'product': product,
            'quantity': item['quantity'],
            'unit_price': product.price,
            'total_price': item_total
        })

    # Create transaction
    transaction = Transaction(
        transaction_number=transaction_number,
        total_amount=total_amount
    )
    db.session.add(transaction)
    db.session.flush()  # Get transaction ID

    # Process each item
    for item_data in transaction_items:
        product = item_data['product']
        quantity = item_data['quantity']

        # Update product stock and sold count
        product.stock -= quantity
        product.sold += quantity

        # Create transaction item
        transaction_item = TransactionItem(
            transaction_id=transaction.id,
            product_id=product.id,
            quantity=quantity,
            unit_price=item_data['unit_price'],
            total_price=item_data['total_price']
        )
        db.session.add(transaction_item)

        # Create sale record
        sale = Sale(
            product_id=product.id,
            quantity=quantity,
            unit_price=item_data['unit_price'],
            total_price=item_data['total_price']
        )
        db.session.add(sale)

    # Handle credit transaction
    if payment_method == 'credit' and customer_id:
        customer = Customer.query.get(customer_id)
        if customer:
            # Create credit transaction
            credit_transaction = CreditTransaction(
                customer_id=customer_id,
                transaction_id=transaction.id,
                amount=total_amount,
                transaction_type='credit',
                description=f'Purchase on credit - {transaction_number}'
            )
            db.session.add(credit_transaction)

            # Update customer total credit
            customer.total_credit += total_amount
            customer.updated_at = datetime.now(timezone.utc)

    db.session.commit()

    response_data = {
        'success': True,
        'message': 'Sale processed successfully',
        'transaction_number': transaction_number,
        'total_amount': total_amount,
        'payment_method': payment_method
    }

    if payment_method == 'credit' and customer_id:
        customer = Customer.query.get(customer_id)
        response_data['customer_name'] = customer.name
        response_data['message'] = f'Credit sale processed for {customer.name}'

    return jsonify(response_data)

@app.route('/reports')
def reports():
    return render_template('reports.html')

@app.route('/sales_report')
def sales_report():
    try:
        # Get sales data grouped by product
        sales_data = db.session.query(
            Product.name,
            db.func.sum(Sale.quantity).label('total_sold'),
            db.func.sum(Sale.total_price).label('total_revenue')
        ).join(Sale).group_by(Product.id, Product.name).all()

        # Get daily sales
        daily_sales_raw = db.session.query(
            db.func.date(Sale.sale_date).label('sale_date'),
            db.func.sum(Sale.total_price).label('daily_revenue'),
            db.func.sum(Sale.quantity).label('daily_quantity')
        ).group_by(db.func.date(Sale.sale_date)).order_by(db.func.date(Sale.sale_date).desc()).limit(30).all()

        # Convert string dates to datetime objects for template
        daily_sales = []
        for row in daily_sales_raw:
            if isinstance(row.sale_date, str):
                sale_date = datetime.strptime(row.sale_date, '%Y-%m-%d').date()
            else:
                sale_date = row.sale_date
            daily_sales.append({
                'sale_date': sale_date,
                'daily_revenue': row.daily_revenue,
                'daily_quantity': row.daily_quantity
            })

        return render_template('sales_report.html', sales_data=sales_data, daily_sales=daily_sales)

    except Exception as e:
        flash(f'Error loading sales report: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/inventory_report')
def inventory_report():
    try:
        products = Product.query.all()
        low_stock_products = Product.query.filter(Product.stock <= 5).all()
        out_of_stock_products = Product.query.filter(Product.stock == 0).all()

        # Calculate summary data
        total_revenue = sum(product.total_revenue for product in products)
        total_profit = sum(product.total_profit for product in products)
        total_stock_value = sum(product.stock * product.price for product in products)
        total_products = len(products)
        total_stock_units = sum(product.stock for product in products)

        return render_template('inventory_report.html',
                             products=products,
                             low_stock_products=low_stock_products,
                             out_of_stock_products=out_of_stock_products,
                             total_revenue=total_revenue,
                             total_profit=total_profit,
                             total_stock_value=total_stock_value,
                             total_products=total_products,
                             total_stock_units=total_stock_units)

    except Exception as e:
        flash(f'Error loading inventory report: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/transaction_history')
def transaction_history():
    try:
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(100).all()
        today = date.today()

        return render_template('transaction_history.html', transactions=transactions, today=today)

    except Exception as e:
        flash(f'Error loading transaction history: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/api/product/<int:id>')
def get_product(id):
    product = Product.query.get_or_404(id)
    return jsonify({
        'id': product.id,
        'name': product.name,
        'price': product.price,
        'stock': product.stock
    })

# Advanced Filtering Routes
@app.route('/sales_filter', methods=['GET', 'POST'])
def sales_filter():
    # Default to last 30 days
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)

    if request.method == 'POST':
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

    # Get sales data for the date range
    sales_data = db.session.query(
        Product.name,
        db.func.sum(Sale.quantity).label('total_sold'),
        db.func.sum(Sale.total_price).label('total_revenue')
    ).join(Sale).filter(
        db.func.date(Sale.sale_date) >= start_date,
        db.func.date(Sale.sale_date) <= end_date
    ).group_by(Product.id, Product.name).all()

    # Get daily sales for the period
    daily_sales_raw = db.session.query(
        db.func.date(Sale.sale_date).label('sale_date'),
        db.func.sum(Sale.total_price).label('daily_revenue'),
        db.func.sum(Sale.quantity).label('daily_quantity')
    ).filter(
        db.func.date(Sale.sale_date) >= start_date,
        db.func.date(Sale.sale_date) <= end_date
    ).group_by(db.func.date(Sale.sale_date)).order_by(db.func.date(Sale.sale_date).desc()).all()

    # Convert string dates to datetime objects for template
    daily_sales = []
    for row in daily_sales_raw:
        if isinstance(row.sale_date, str):
            sale_date = datetime.strptime(row.sale_date, '%Y-%m-%d').date()
        else:
            sale_date = row.sale_date
        daily_sales.append({
            'sale_date': sale_date,
            'daily_revenue': row.daily_revenue,
            'daily_quantity': row.daily_quantity
        })

    return render_template('sales_filter.html',
                         sales_data=sales_data,
                         daily_sales=daily_sales,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/inventory_movements')
def inventory_movements():
    # Get products with recent activity
    products = Product.query.all()

    # Calculate movements with better data
    movements = []
    for product in products:
        # Calculate stock turnover rate
        turnover_rate = (product.sold / product.received * 100) if product.received > 0 else 0

        movements.append({
            'product': product,
            'received_total': product.received,
            'sold_total': product.sold,
            'current_stock': product.stock,
            'stock_value': product.stock * product.price,
            'turnover_rate': turnover_rate,
            'stock_status': 'Low Stock' if product.stock <= 5 else 'In Stock' if product.stock > 0 else 'Out of Stock'
        })

    return render_template('inventory_movements.html', movements=movements)

# Customer Management Routes
@app.route('/customers')
def customers():
    customers = Customer.query.all()
    return render_template('customers.html', customers=customers)

@app.route('/add_customer', methods=['GET', 'POST'])
def add_customer():
    if request.method == 'POST':
        name = request.form['name']
        phone = request.form.get('phone', '')
        address = request.form.get('address', '')

        customer = Customer(name=name, phone=phone, address=address)
        db.session.add(customer)
        db.session.commit()
        flash('Customer added successfully!', 'success')
        return redirect(url_for('customers'))

    return render_template('add_customer.html')

@app.route('/edit_customer/<int:id>', methods=['GET', 'POST'])
def edit_customer(id):
    customer = Customer.query.get_or_404(id)

    if request.method == 'POST':
        customer.name = request.form['name']
        customer.phone = request.form.get('phone', '')
        customer.address = request.form.get('address', '')
        customer.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        flash('Customer updated successfully!', 'success')
        return redirect(url_for('customers'))

    return render_template('edit_customer.html', customer=customer)

# Credit Management Routes
@app.route('/credit_management')
def credit_management():
    customers_with_credit = Customer.query.filter(Customer.total_credit > Customer.total_paid).all()
    recent_credits = CreditTransaction.query.order_by(CreditTransaction.created_at.desc()).limit(20).all()
    return render_template('credit_management.html',
                         customers_with_credit=customers_with_credit,
                         recent_credits=recent_credits)

@app.route('/customer_credit/<int:customer_id>')
def customer_credit(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    credit_history = CreditTransaction.query.filter_by(customer_id=customer_id).order_by(CreditTransaction.created_at.desc()).all()
    return render_template('customer_credit.html', customer=customer, credit_history=credit_history)

@app.route('/add_credit/<int:customer_id>', methods=['POST'])
def add_credit(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    amount = float(request.form['amount'])
    description = request.form.get('description', '')

    # Create credit transaction
    credit_transaction = CreditTransaction(
        customer_id=customer_id,
        amount=amount,
        transaction_type='credit',
        description=description
    )
    db.session.add(credit_transaction)

    # Update customer total credit
    customer.total_credit += amount
    customer.updated_at = datetime.now(timezone.utc)

    db.session.commit()
    flash(f'Credit of ₱{amount:.2f} added for {customer.name}', 'success')
    return redirect(url_for('customer_credit', customer_id=customer_id))

@app.route('/add_payment/<int:customer_id>', methods=['POST'])
def add_payment(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    amount = float(request.form['amount'])
    description = request.form.get('description', 'Payment received')

    # Create payment transaction
    payment_transaction = CreditTransaction(
        customer_id=customer_id,
        amount=amount,
        transaction_type='payment',
        description=description,
        is_paid=True
    )
    db.session.add(payment_transaction)

    # Update customer total paid
    customer.total_paid += amount
    customer.updated_at = datetime.now(timezone.utc)

    db.session.commit()
    flash(f'Payment of ₱{amount:.2f} recorded for {customer.name}', 'success')
    return redirect(url_for('customer_credit', customer_id=customer_id))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Initialize with sample data if database is empty
        if Product.query.count() == 0:
            from init_data import init_sample_data
            init_sample_data()

    # Get port from environment variable or default to 5000
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') != 'production'

    app.run(host='0.0.0.0', port=port, debug=debug)
