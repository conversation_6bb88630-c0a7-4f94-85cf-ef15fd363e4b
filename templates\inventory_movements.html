{% extends "base.html" %} {% block title %}Inventory Movements - ATE MEG's
FROZEN FOODS{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1><i class="fas fa-exchange-alt"></i> Inventory Movements</h1>
      <a href="{{ url_for('reports') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Reports
      </a>
    </div>
  </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body text-center">
        <h5>Total Products</h5>
        <h3>{{ movements|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Total Stock Value</h5>
        <h3>₱{{ "%.2f"|format(movements|sum(attribute='stock_value')) }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body text-center">
        <h5>Items in Stock</h5>
        <h3>{{ movements|sum(attribute='current_stock') }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body text-center">
        <h5>Items Sold</h5>
        <h3>{{ movements|sum(attribute='sold_total') }}</h3>
      </div>
    </div>
  </div>
</div>

<!-- Inventory Movements Table -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-list"></i> Product Movement Summary
        </h5>
      </div>
      <div class="card-body">
        {% if movements %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Product</th>
                <th>Current Price</th>
                <th>Current Stock</th>
                <th>Total Received</th>
                <th>Total Sold</th>
                <th>Stock Value</th>
                <th>Movement Status</th>
                <th>Stock Level</th>
              </tr>
            </thead>
            <tbody>
              {% for movement in movements|sort(attribute='current_stock') %}
              <tr
                class="{% if movement.current_stock == 0 %}table-danger{% elif movement.current_stock <= 5 %}table-warning{% endif %}"
              >
                <td>
                  <strong>{{ movement.product.name }}</strong>
                </td>
                <td>₱{{ "%.2f"|format(movement.product.price) }}</td>
                <td>
                  <span
                    class="badge {% if movement.current_stock == 0 %}bg-danger{% elif movement.current_stock <= 5 %}bg-warning{% else %}bg-success{% endif %}"
                  >
                    {{ movement.current_stock }}
                  </span>
                </td>
                <td>{{ movement.received_total }}</td>
                <td>{{ movement.sold_total }}</td>
                <td class="text-success">
                  ₱{{ "%.2f"|format(movement.stock_value) }}
                </td>
                <td>
                  {% if movement.current_stock == 0 %}
                  <span class="badge bg-danger">Out of Stock</span>
                  {% elif movement.current_stock <= 5 %}
                  <span class="badge bg-warning">Low Stock</span>
                  {% elif movement.current_stock <= 10 %}
                  <span class="badge bg-info">Medium Stock</span>
                  {% else %}
                  <span class="badge bg-success">Well Stocked</span>
                  {% endif %}
                </td>
                <td>
                  {% set max_stock =
                  movements|map(attribute='current_stock')|max %} {% set
                  percentage = (movement.current_stock / max_stock * 100) if
                  max_stock > 0 else 0 %}
                  <div class="progress" style="height: 20px">
                    <div
                      class="progress-bar {% if movement.current_stock == 0 %}bg-danger{% elif movement.current_stock <= 5 %}bg-warning{% else %}bg-success{% endif %}"
                      role="progressbar"
                      style="width: {{ percentage }}%"
                      aria-valuenow="{{ percentage }}"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    >
                      {% if percentage > 15 %}{{ movement.current_stock }}{%
                      endif %}
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No inventory movements found</h4>
          <p class="text-muted">Add products to start tracking movements.</p>
          <a href="{{ url_for('add_product') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Products
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Movement Analysis -->
{% if movements %}
<div class="row mt-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-pie"></i> Stock Status Distribution
        </h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-3">
            <h4 class="text-success">
              {{ movements|selectattr('current_stock', '>', 10)|list|length }}
            </h4>
            <small class="text-muted">Well Stocked</small>
          </div>
          <div class="col-3">
            <h4 class="text-info">
              {{ movements|selectattr('current_stock', '>',
              5)|selectattr('current_stock', '<=', 10)|list|length }}
            </h4>
            <small class="text-muted">Medium Stock</small>
          </div>
          <div class="col-3">
            <h4 class="text-warning">
              {{ movements|selectattr('current_stock', '>',
              0)|selectattr('current_stock', '<=', 5)|list|length }}
            </h4>
            <small class="text-muted">Low Stock</small>
          </div>
          <div class="col-3">
            <h4 class="text-danger">
              {{ movements|selectattr('current_stock', '==', 0)|list|length }}
            </h4>
            <small class="text-muted">Out of Stock</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-bar"></i> Top Moving Products
        </h5>
      </div>
      <div class="card-body">
        {% for movement in movements|sort(attribute='sold_this_month',
        reverse=true)[:5] %}
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <strong>{{ movement.product.name }}</strong><br />
            <small class="text-muted"
              >{{ movement.sold_this_month }} sold</small
            >
          </div>
          <div class="text-end">
            <span class="badge bg-primary"
              >{{ movement.current_stock }} left</span
            >
          </div>
        </div>
        {% if not loop.last %}
        <hr class="my-2" />
        {% endif %} {% endfor %}
      </div>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tools"></i> Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <a
              href="{{ url_for('products') }}"
              class="btn btn-primary w-100 mb-2"
            >
              <i class="fas fa-box"></i> Manage Products
            </a>
          </div>
          <div class="col-md-3">
            <a
              href="{{ url_for('add_product') }}"
              class="btn btn-success w-100 mb-2"
            >
              <i class="fas fa-plus"></i> Add New Product
            </a>
          </div>
          <div class="col-md-3">
            <a
              href="{{ url_for('sales_filter') }}"
              class="btn btn-info w-100 mb-2"
            >
              <i class="fas fa-filter"></i> Filter Sales
            </a>
          </div>
          <div class="col-md-3">
            <a
              href="{{ url_for('inventory_report') }}"
              class="btn btn-warning w-100 mb-2"
            >
              <i class="fas fa-warehouse"></i> Inventory Report
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %}
