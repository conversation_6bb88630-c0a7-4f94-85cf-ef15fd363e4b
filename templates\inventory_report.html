{% extends "base.html" %} {% block title %}Inventory Report - Frozen Foods POS{%
endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1><i class="fas fa-warehouse"></i> Inventory Report</h1>
      <a href="{{ url_for('reports') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Reports
      </a>
    </div>
  </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body text-center">
        <h5>Total Products</h5>
        <h3>{{ products|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Total Stock</h5>
        <h3>{{ total_stock_units }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body text-center">
        <h5>Low Stock Items</h5>
        <h3>{{ low_stock_products|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-danger">
      <div class="card-body text-center">
        <h5>Out of Stock</h5>
        <h3>{{ out_of_stock_products|length }}</h3>
      </div>
    </div>
  </div>
</div>

<!-- Profit Summary -->
<div class="row mb-4">
  <div class="col-md-4">
    <div class="card text-white bg-info">
      <div class="card-body text-center">
        <h5>Total Revenue</h5>
        <h3>₱{{ "%.2f"|format(total_revenue) }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Total Profit</h5>
        <h3>₱{{ "%.2f"|format(total_profit) }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card text-white bg-secondary">
      <div class="card-body text-center">
        <h5>Stock Value</h5>
        <h3>₱{{ "%.2f"|format(total_stock_value) }}</h3>
      </div>
    </div>
  </div>
</div>

<!-- Low Stock Alert -->
{% if low_stock_products %}
<div class="row mb-4">
  <div class="col-12">
    <div class="alert alert-warning">
      <h5 class="alert-heading">
        <i class="fas fa-exclamation-triangle"></i> Low Stock Alert
      </h5>
      <p class="mb-0">
        You have {{ low_stock_products|length }} product(s) with low stock
        levels. Consider restocking these items soon.
      </p>
    </div>
  </div>
</div>
{% endif %}

<!-- Inventory Table -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Complete Inventory</h5>
      </div>
      <div class="card-body">
        {% if products %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Product</th>
                <th>Selling Price</th>
                <th>Capital Price</th>
                <th>Profit/Unit</th>
                <th>Received</th>
                <th>Sold</th>
                <th>Current Stock</th>
                <th>Stock Value</th>
                <th>Total Profit</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {% for product in products|sort(attribute='stock') %}
              <tr
                class="{% if product.stock == 0 %}table-danger{% elif product.stock <= 5 %}table-warning{% endif %}"
              >
                <td>
                  <strong>{{ product.name }}</strong>
                </td>
                <td>₱{{ "%.2f"|format(product.price) }}</td>
                <td>₱{{ "%.2f"|format(product.capital_price) }}</td>
                <td class="text-success">
                  ₱{{ "%.2f"|format(product.profit_per_unit) }}
                </td>
                <td>{{ product.received }}</td>
                <td>{{ product.sold }}</td>
                <td>
                  <span
                    class="badge {% if product.stock == 0 %}bg-danger{% elif product.stock <= 5 %}bg-warning{% else %}bg-success{% endif %}"
                  >
                    {{ product.stock }}
                  </span>
                </td>
                <td class="text-info">
                  ₱{{ "%.2f"|format(product.stock * product.price) }}
                </td>
                <td class="text-success">
                  <strong>₱{{ "%.2f"|format(product.total_profit) }}</strong>
                </td>
                <td>
                  {% if product.stock == 0 %}
                  <span class="badge bg-danger">Out of Stock</span>
                  {% elif product.stock <= 5 %}
                  <span class="badge bg-warning">Low Stock</span>
                  {% elif product.stock <= 10 %}
                  <span class="badge bg-info">Medium Stock</span>
                  {% else %}
                  <span class="badge bg-success">Well Stocked</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot class="table-secondary">
              <tr>
                <th>TOTALS</th>
                <th>-</th>
                <th>{{ products|sum(attribute='received') }}</th>
                <th>{{ products|sum(attribute='sold') }}</th>
                <th>{{ products|sum(attribute='stock') }}</th>
                <th class="text-success">
                  ₱{{
                  "%.2f"|format(products|map('stock')|list|zip(products|map('price')|list)|map('product')|sum)
                  }}
                </th>
                <th>-</th>
                <th>-</th>
              </tr>
            </tfoot>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No products in inventory</h4>
          <p class="text-muted">Add products to start tracking inventory.</p>
          <a href="{{ url_for('add_product') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Products
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Stock Analysis -->
{% if products %}
<div class="row mt-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-pie"></i> Stock Distribution
        </h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-3">
            <h4 class="text-success">
              {{ products|selectattr('stock', '>', 10)|list|length }}
            </h4>
            <small class="text-muted">Well Stocked</small>
          </div>
          <div class="col-3">
            <h4 class="text-info">
              {{ products|selectattr('stock', '>', 5)|selectattr('stock', '<=',
              10)|list|length }}
            </h4>
            <small class="text-muted">Medium Stock</small>
          </div>
          <div class="col-3">
            <h4 class="text-warning">
              {{ products|selectattr('stock', '>', 0)|selectattr('stock', '<=',
              5)|list|length }}
            </h4>
            <small class="text-muted">Low Stock</small>
          </div>
          <div class="col-3">
            <h4 class="text-danger">
              {{ products|selectattr('stock', '==', 0)|list|length }}
            </h4>
            <small class="text-muted">Out of Stock</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-dollar-sign"></i> Inventory Value</h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6">
            <h4 class="text-primary">
              ₱{{
              "%.2f"|format(products|map('stock')|list|zip(products|map('price')|list)|map('product')|sum)
              }}
            </h4>
            <small class="text-muted">Total Stock Value</small>
          </div>
          <div class="col-6">
            <h4 class="text-success">
              ₱{{
              "%.2f"|format((products|map('stock')|list|zip(products|map('price')|list)|map('product')|sum)
              / products|length if products else 0) }}
            </h4>
            <small class="text-muted">Avg. Product Value</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %}
