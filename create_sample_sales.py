#!/usr/bin/env python3
"""
Create sample sales data for testing reports
"""

from app import app, db, Product, Sale, Transaction, TransactionItem, Customer, CreditTransaction
from datetime import datetime, timedelta
import random

def create_sample_sales():
    """Create sample sales data for testing"""
    
    with app.app_context():
        print("🛒 Creating sample sales data...")
        
        # Get all products and customers
        products = Product.query.all()
        customers = Customer.query.all()
        
        if not products:
            print("❌ No products found. Please run init_data.py first.")
            return
        
        # Create sample transactions for the last 7 days
        for days_ago in range(7):
            transaction_date = datetime.now() - timedelta(days=days_ago)
            
            # Create 2-5 transactions per day
            num_transactions = random.randint(2, 5)
            
            for i in range(num_transactions):
                # Generate transaction number
                transaction_number = f"TXN{transaction_date.strftime('%Y%m%d')}{i+1:03d}"
                
                # Select random products for this transaction
                num_items = random.randint(1, 4)
                selected_products = random.sample(products, min(num_items, len(products)))
                
                total_amount = 0
                transaction_items = []
                
                for product in selected_products:
                    if product.stock > 0:
                        quantity = random.randint(1, min(3, product.stock))
                        item_total = quantity * product.price
                        total_amount += item_total
                        
                        transaction_items.append({
                            'product': product,
                            'quantity': quantity,
                            'unit_price': product.price,
                            'total_price': item_total
                        })
                
                if transaction_items:
                    # Create transaction
                    transaction = Transaction(
                        transaction_number=transaction_number,
                        total_amount=total_amount,
                        transaction_date=transaction_date
                    )
                    db.session.add(transaction)
                    db.session.flush()
                    
                    # Decide if this is a credit sale (30% chance)
                    is_credit = random.random() < 0.3 and customers
                    
                    for item_data in transaction_items:
                        product = item_data['product']
                        quantity = item_data['quantity']
                        
                        # Update product stock and sold count
                        product.stock -= quantity
                        product.sold += quantity
                        
                        # Create transaction item
                        transaction_item = TransactionItem(
                            transaction_id=transaction.id,
                            product_id=product.id,
                            quantity=quantity,
                            unit_price=item_data['unit_price'],
                            total_price=item_data['total_price']
                        )
                        db.session.add(transaction_item)
                        
                        # Create sale record
                        sale = Sale(
                            product_id=product.id,
                            quantity=quantity,
                            unit_price=item_data['unit_price'],
                            total_price=item_data['total_price'],
                            sale_date=transaction_date
                        )
                        db.session.add(sale)
                    
                    # Handle credit transaction
                    if is_credit:
                        customer = random.choice(customers)
                        credit_transaction = CreditTransaction(
                            customer_id=customer.id,
                            transaction_id=transaction.id,
                            amount=total_amount,
                            transaction_type='credit',
                            description=f'Purchase on credit - {transaction_number}',
                            created_at=transaction_date
                        )
                        db.session.add(credit_transaction)
                        
                        # Update customer total credit
                        customer.total_credit += total_amount
                        customer.updated_at = transaction_date
                    
                    print(f"✅ Created transaction {transaction_number} - ₱{total_amount:.2f} ({'Credit' if is_credit else 'Cash'})")
        
        db.session.commit()
        print(f"\n🎉 Sample sales data created successfully!")
        
        # Show summary
        total_sales = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
        total_transactions = Transaction.query.count()
        
        print(f"\n📊 Sales Summary:")
        print(f"Total Transactions: {total_transactions}")
        print(f"Total Sales: ₱{total_sales:.2f}")
        print(f"Average Transaction: ₱{total_sales/total_transactions:.2f}" if total_transactions > 0 else "No transactions")

if __name__ == "__main__":
    create_sample_sales()
