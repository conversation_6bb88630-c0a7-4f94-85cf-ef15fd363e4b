{% extends "base.html" %} {% block title %}Sales Filter - ATE MEG's FROZEN
FOODS{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1><i class="fas fa-filter"></i> Sales Filter & Analytics</h1>
      <a href="{{ url_for('reports') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Reports
      </a>
    </div>
  </div>
</div>

<!-- Date Filter Form -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-calendar-alt"></i> Filter by Date Range
        </h5>
      </div>
      <div class="card-body">
        <form method="POST" class="row g-3">
          <div class="col-md-4">
            <label for="start_date" class="form-label">Start Date</label>
            <input
              type="date"
              class="form-control"
              id="start_date"
              name="start_date"
              value="{{ start_date }}"
              required
            />
          </div>
          <div class="col-md-4">
            <label for="end_date" class="form-label">End Date</label>
            <input
              type="date"
              class="form-control"
              id="end_date"
              name="end_date"
              value="{{ end_date }}"
              required
            />
          </div>
          <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Filter Sales
              </button>
            </div>
          </div>
        </form>

        <!-- Quick Filter Buttons -->
        <div class="mt-3">
          <h6>Quick Filters:</h6>
          <div class="btn-group" role="group">
            <button
              class="btn btn-outline-primary btn-sm"
              onclick="setDateRange(1)"
            >
              Today
            </button>
            <button
              class="btn btn-outline-primary btn-sm"
              onclick="setDateRange(7)"
            >
              Last 7 Days
            </button>
            <button
              class="btn btn-outline-primary btn-sm"
              onclick="setDateRange(30)"
            >
              Last 30 Days
            </button>
            <button
              class="btn btn-outline-primary btn-sm"
              onclick="setDateRange(90)"
            >
              Last 3 Months
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body text-center">
        <h5>Total Revenue</h5>
        <h3>
          ₱{{ "%.2f"|format(sales_data|sum(attribute='total_revenue') or 0) }}
        </h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Items Sold</h5>
        <h3>{{ sales_data|sum(attribute='total_sold') or 0 }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body text-center">
        <h5>Products Sold</h5>
        <h3>{{ sales_data|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body text-center">
        <h5>Avg. Sale Value</h5>
        <h3>
          ₱{{ "%.2f"|format((sales_data|sum(attribute='total_revenue') /
          sales_data|sum(attribute='total_sold')) if
          sales_data|sum(attribute='total_sold') > 0 else 0) }}
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Product Sales Analysis -->
<div class="row">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-bar"></i> Product Performance ({{ start_date }}
          to {{ end_date }})
        </h5>
      </div>
      <div class="card-body">
        {% if sales_data %}
        <div class="table-responsive">
          <table class="table table-striped">
            <thead class="table-dark">
              <tr>
                <th>Product</th>
                <th>Quantity Sold</th>
                <th>Revenue</th>
                <th>Avg. Price</th>
                <th>Performance %</th>
              </tr>
            </thead>
            <tbody>
              {% for item in sales_data|sort(attribute='total_revenue',
              reverse=true) %}
              <tr>
                <td><strong>{{ item.name }}</strong></td>
                <td>{{ item.total_sold }}</td>
                <td class="text-success">
                  ₱{{ "%.2f"|format(item.total_revenue) }}
                </td>
                <td>
                  ₱{{ "%.2f"|format(item.total_revenue / item.total_sold) }}
                </td>
                <td>
                  {% set percentage = (item.total_revenue /
                  (sales_data|sum(attribute='total_revenue')) * 100) if
                  sales_data|sum(attribute='total_revenue') > 0 else 0 %}
                  <div class="progress" style="height: 20px">
                    <div
                      class="progress-bar bg-success"
                      role="progressbar"
                      style="width: {{ percentage }}%"
                      aria-valuenow="{{ percentage }}"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    >
                      {{ "%.1f"|format(percentage) }}%
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No sales data for selected period</h4>
          <p class="text-muted">
            Try selecting a different date range or check if there are any sales
            recorded.
          </p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Performers</h5>
      </div>
      <div class="card-body">
        {% if sales_data %} {% for item in
        sales_data|sort(attribute='total_revenue', reverse=true) %} {% if
        loop.index <= 5 %}
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <strong>{{ item.name }}</strong><br />
            <small class="text-muted">{{ item.total_sold }} units</small>
          </div>
          <div class="text-end">
            <span class="badge bg-success"
              >₱{{ "%.2f"|format(item.total_revenue) }}</span
            >
          </div>
        </div>
        {% if not loop.last %}
        <hr class="my-2" />
        {% endif %} {% endif %} {% endfor %} {% else %}
        <p class="text-muted text-center">No data available</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Daily Sales Trend -->
{% if daily_sales %}
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-calendar-alt"></i> Daily Sales Trend
        </h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead class="table-dark">
              <tr>
                <th>Date</th>
                <th>Items Sold</th>
                <th>Revenue</th>
                <th>Trend</th>
              </tr>
            </thead>
            <tbody>
              {% for day in daily_sales %}
              <tr>
                <td>
                  {{ day.sale_date.strftime('%Y-%m-%d') if day.sale_date else
                  'N/A' }}
                </td>
                <td>{{ day.daily_quantity }}</td>
                <td class="text-success">
                  ₱{{ "%.2f"|format(day.daily_revenue) }}
                </td>
                <td>
                  {% set max_revenue =
                  daily_sales|map(attribute='daily_revenue')|max %} {% set
                  percentage = (day.daily_revenue / max_revenue * 100) if
                  max_revenue > 0 else 0 %}
                  <div class="progress" style="height: 15px">
                    <div
                      class="progress-bar bg-info"
                      role="progressbar"
                      style="width: {{ percentage }}%"
                    ></div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %} {% block scripts %}
<script>
  function setDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();

    if (days === 1) {
      // Today only
      startDate.setDate(endDate.getDate());
    } else {
      startDate.setDate(endDate.getDate() - days + 1);
    }

    document.getElementById("start_date").value = startDate
      .toISOString()
      .split("T")[0];
    document.getElementById("end_date").value = endDate
      .toISOString()
      .split("T")[0];
  }

  // Set max date to today
  document.addEventListener("DOMContentLoaded", function () {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("start_date").max = today;
    document.getElementById("end_date").max = today;
  });
</script>
{% endblock %}
