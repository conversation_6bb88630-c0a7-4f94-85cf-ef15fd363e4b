#!/usr/bin/env python3
"""
Verify the inventory report is working correctly
"""

from app import app, Product

def verify_inventory_report():
    """Verify inventory report calculations"""
    with app.app_context():
        print("🔍 VERIFYING INVENTORY REPORT")
        print("=" * 60)
        
        products = Product.query.all()
        print(f"📦 Found {len(products)} products")
        
        print(f"\n{'Product':<20} {'Price':<8} {'Capital':<8} {'Profit':<8} {'Recv':<6} {'Sold':<6} {'Stock':<6} {'Status':<12}")
        print("-" * 80)
        
        total_revenue = 0
        total_profit = 0
        total_stock_value = 0
        low_stock_count = 0
        out_of_stock_count = 0
        
        for product in products:
            capital_price = round(product.price * 0.7, 2)
            profit_per_unit = round(product.price - capital_price, 2)
            total_product_profit = round(product.sold * profit_per_unit, 2)
            total_product_revenue = round(product.sold * product.price, 2)
            stock_value = round(product.stock * product.price, 2)
            
            status = "Out of Stock" if product.stock == 0 else "Low Stock" if product.stock <= 5 else "In Stock"
            
            print(f"{product.name:<20} ₱{product.price:<7.2f} ₱{capital_price:<7.2f} ₱{profit_per_unit:<7.2f} {product.received:<6} {product.sold:<6} {product.stock:<6} {status:<12}")
            
            total_revenue += total_product_revenue
            total_profit += total_product_profit
            total_stock_value += stock_value
            
            if product.stock == 0:
                out_of_stock_count += 1
            elif product.stock <= 5:
                low_stock_count += 1
        
        print("\n" + "=" * 60)
        print("📊 SUMMARY:")
        print(f"  • Total Products: {len(products)}")
        print(f"  • Total Stock Units: {sum(p.stock for p in products)}")
        print(f"  • Low Stock Items: {low_stock_count}")
        print(f"  • Out of Stock Items: {out_of_stock_count}")
        print(f"  • Total Revenue: ₱{total_revenue:,.2f}")
        print(f"  • Total Profit: ₱{total_profit:,.2f}")
        print(f"  • Total Stock Value: ₱{total_stock_value:,.2f}")
        
        print("\n🎯 CHICKEN NUGGETS ANALYSIS:")
        nuggets = next((p for p in products if 'Nuggets' in p.name), None)
        if nuggets:
            nuggets_capital = round(nuggets.price * 0.7, 2)
            nuggets_profit_per_unit = round(nuggets.price - nuggets_capital, 2)
            nuggets_total_profit = round(nuggets.sold * nuggets_profit_per_unit, 2)
            nuggets_revenue = round(nuggets.sold * nuggets.price, 2)
            
            print(f"  • Product: {nuggets.name}")
            print(f"  • Selling Price: ₱{nuggets.price}")
            print(f"  • Capital Price: ₱{nuggets_capital}")
            print(f"  • Profit per Unit: ₱{nuggets_profit_per_unit}")
            print(f"  • Received: {nuggets.received} units")
            print(f"  • Sold: {nuggets.sold} units")
            print(f"  • Stock: {nuggets.stock} units")
            print(f"  • Total Revenue: ₱{nuggets_revenue}")
            print(f"  • Total Profit: ₱{nuggets_total_profit}")
            print(f"  • Status: {'Out of Stock' if nuggets.stock == 0 else 'Low Stock' if nuggets.stock <= 5 else 'In Stock'}")
        
        print("\n✅ Inventory report verification complete!")

if __name__ == "__main__":
    verify_inventory_report()
