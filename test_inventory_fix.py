#!/usr/bin/env python3
"""
Test the inventory report fix
"""

from app import app, Product

def test_inventory_report():
    """Test that inventory report works without iteration errors"""
    with app.app_context():
        print("🧪 Testing inventory report fix...")
        
        # Test that we can get products as a list
        products = Product.query.all()
        print(f"✅ Retrieved {len(products)} products successfully")
        
        # Test that products is iterable
        try:
            for product in products:
                print(f"  - {product.name}: Stock={product.stock}, Sold={product.sold}")
                break  # Just test first one
            print("✅ Products list is iterable")
        except Exception as e:
            print(f"❌ Products iteration error: {e}")
            return False
        
        # Test individual product properties
        if products:
            product = products[0]
            try:
                print(f"✅ Product properties work:")
                print(f"  - Capital Price: ₱{product.capital_price}")
                print(f"  - Profit per Unit: ₱{product.profit_per_unit}")
                print(f"  - Total Profit: ₱{product.total_profit}")
                print(f"  - Total Revenue: ₱{product.total_revenue}")
            except Exception as e:
                print(f"❌ Product property error: {e}")
                return False
        
        print("✅ All inventory report tests passed!")
        return True

if __name__ == "__main__":
    test_inventory_report()
