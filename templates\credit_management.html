{% extends "base.html" %}

{% block title %}Credit Management (Pautang) - ATE MEG's FROZEN FOODS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-credit-card"></i> Credit Management (Pautang System)
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <h5>Customers with Debt</h5>
                <h3>{{ customers_with_credit|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <h5>Total Outstanding</h5>
                <h3>₱{{ "%.2f"|format(customers_with_credit|map(attribute='balance')|sum) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <h5>Total Collected</h5>
                <h3>₱{{ "%.2f"|format(customers_with_credit|sum(attribute='total_paid')) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <h5>Total Credit Given</h5>
                <h3>₱{{ "%.2f"|format(customers_with_credit|sum(attribute='total_credit')) }}</h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Customers with Outstanding Credit -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Customers with Outstanding Credit
                </h5>
            </div>
            <div class="card-body">
                {% if customers_with_credit %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Customer</th>
                                <th>Total Credit</th>
                                <th>Paid</th>
                                <th>Outstanding</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers_with_credit|sort(attribute='balance', reverse=true) %}
                            <tr class="table-warning">
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.phone %}
                                    <br><small class="text-muted">{{ customer.phone }}</small>
                                    {% endif %}
                                </td>
                                <td class="text-danger">₱{{ "%.2f"|format(customer.total_credit) }}</td>
                                <td class="text-success">₱{{ "%.2f"|format(customer.total_paid) }}</td>
                                <td>
                                    <span class="badge bg-warning text-dark">₱{{ "%.2f"|format(customer.balance) }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('customer_credit', customer_id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-success" 
                                                onclick="recordPayment({{ customer.id }}, '{{ customer.name }}', {{ customer.balance }})"
                                                title="Record Payment">
                                            <i class="fas fa-money-bill"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h4 class="text-success">All Clear!</h4>
                    <p class="text-muted">No customers have outstanding credit at the moment.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Credit Transactions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent Transactions
                </h5>
            </div>
            <div class="card-body">
                {% if recent_credits %}
                <div class="timeline">
                    {% for credit in recent_credits[:10] %}
                    <div class="timeline-item mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>{{ credit.customer.name }}</strong><br>
                                <small class="text-muted">{{ credit.created_at.strftime('%m/%d %H:%M') }}</small>
                            </div>
                            <div class="text-end">
                                {% if credit.transaction_type == 'credit' %}
                                    <span class="badge bg-danger">-₱{{ "%.2f"|format(credit.amount) }}</span>
                                {% else %}
                                    <span class="badge bg-success">+₱{{ "%.2f"|format(credit.amount) }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% if credit.description %}
                        <small class="text-muted">{{ credit.description }}</small>
                        {% endif %}
                    </div>
                    {% if not loop.last %}<hr class="my-2">{% endif %}
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">No recent transactions</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('customers') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-users"></i> Manage Customers
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-user-plus"></i> Add New Customer
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('pos') }}" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-cash-register"></i> POS System
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('reports') }}" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-chart-bar"></i> View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Record Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="paymentForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Customer</label>
                        <input type="text" class="form-control" id="customerName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">Payment Amount (₱)</label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" 
                               step="0.01" min="0" required>
                        <div class="form-text">Outstanding balance: ₱<span id="outstandingBalance"></span></div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="payment_description" name="description" 
                               value="Payment received" placeholder="Payment description">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Record Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function recordPayment(customerId, customerName, balance) {
    document.getElementById('customerName').value = customerName;
    document.getElementById('outstandingBalance').textContent = balance.toFixed(2);
    document.getElementById('payment_amount').max = balance;
    document.getElementById('paymentForm').action = `/add_payment/${customerId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}
</script>
{% endblock %}
