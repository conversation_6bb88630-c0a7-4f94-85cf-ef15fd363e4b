# Git Commands for Deployment

# Initialize repository and add files
git init
git add .
git commit -m "Initial commit - ATE MEG's FROZEN FOODS POS System"

# Set up GitHub repository
git branch -M main
git remote add origin https://github.com/laganzonj/FROZEN_FOODS.git

# Push to GitHub
git push -u origin main

# For subsequent updates:
# git add .
# git commit -m "Update description"
# git push origin main
