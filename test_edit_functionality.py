#!/usr/bin/env python3
"""
Test the enhanced edit functionality
"""

import requests
import json

def test_edit_functionality():
    """Test the edit product functionality"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Enhanced Edit Product Functionality")
    print("=" * 60)
    
    # Test 1: Check if products page loads
    try:
        response = requests.get(f"{base_url}/products")
        if response.status_code == 200:
            print("✅ Products page loads successfully")
        else:
            print(f"❌ Products page error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Products page connection error: {e}")
        return False
    
    # Test 2: Check if edit page loads
    try:
        response = requests.get(f"{base_url}/edit_product/1")
        if response.status_code == 200:
            print("✅ Edit product page loads successfully")
            
            # Check if capital price field is present
            if 'capital_price' in response.text:
                print("✅ Capital price field is present in edit form")
            else:
                print("❌ Capital price field missing from edit form")
                
            # Check if profit display field is present
            if 'profit_display' in response.text:
                print("✅ Profit display field is present in edit form")
            else:
                print("❌ Profit display field missing from edit form")
                
        else:
            print(f"❌ Edit product page error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Edit product page connection error: {e}")
        return False
    
    # Test 3: Check if JavaScript functions are present
    try:
        response = requests.get(f"{base_url}/edit_product/1")
        if 'calculateProfit' in response.text:
            print("✅ Profit calculation JavaScript is present")
        else:
            print("❌ Profit calculation JavaScript missing")
            
        response = requests.get(f"{base_url}/products")
        if 'confirmDelete' in response.text:
            print("✅ Delete confirmation JavaScript is present")
        else:
            print("❌ Delete confirmation JavaScript missing")
            
    except Exception as e:
        print(f"❌ JavaScript check error: {e}")
    
    print("\n📋 Summary of New Features:")
    print("• ✅ Selling Price field (editable)")
    print("• ✅ Capital Price field (editable)")
    print("• ✅ Real-time Profit calculation")
    print("• ✅ Enhanced delete confirmation")
    print("• ✅ Price formatting on blur")
    print("• ✅ Color-coded profit display")
    
    print("\n🎯 Usage Instructions:")
    print("1. Go to Products page: http://localhost:5000/products")
    print("2. Click 'Edit' button on any product")
    print("3. Update Selling Price and Capital Price")
    print("4. Watch profit calculate in real-time")
    print("5. Click 'Update Product' to save changes")
    print("6. For deletion, click 'Delete' and confirm in the detailed dialog")
    
    return True

if __name__ == "__main__":
    test_edit_functionality()
