{% extends "base.html" %}

{% block title %}Edit Product - Frozen Foods POS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-edit"></i> Edit Product
            </h1>
            <a href="{{ url_for('products') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Product Name *</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ product.name }}" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (₱) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" value="{{ product.price }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="received" class="form-label">Total Received *</label>
                                <input type="number" class="form-control" id="received" name="received" 
                                       min="0" value="{{ product.received }}" required>
                                <div class="form-text">Changing this will adjust current stock accordingly</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Current Stock</label>
                                <input type="text" class="form-control" value="{{ product.stock }}" readonly>
                                <div class="form-text">This is calculated automatically</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Total Sold</label>
                                <input type="text" class="form-control" value="{{ product.sold }}" readonly>
                                <div class="form-text">This is tracked from sales</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i> Product Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-primary">{{ product.received }}</h5>
                        <small class="text-muted">Received</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success">{{ product.sold }}</h5>
                        <small class="text-muted">Sold</small>
                    </div>
                    <div class="col-4">
                        <h5 class="{% if product.stock <= 5 %}text-warning{% else %}text-info{% endif %}">{{ product.stock }}</h5>
                        <small class="text-muted">In Stock</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Edit Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0 small">
                    <li><strong>Name:</strong> Update product name as needed</li>
                    <li><strong>Price:</strong> Adjust selling price</li>
                    <li><strong>Received:</strong> Update total received quantity</li>
                    <li><strong>Stock:</strong> Will auto-adjust based on received quantity changes</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format price input
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('blur', function() {
        if (this.value) {
            this.value = parseFloat(this.value).toFixed(2);
        }
    });
});
</script>
{% endblock %}
