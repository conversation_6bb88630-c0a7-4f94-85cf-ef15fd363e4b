/* ATE MEG's FROZEN FOODS - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --text-light: #ffffff;
    --text-dark: #212529;
    --text-muted: #6c757d;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-radius: 0.375rem;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    color: var(--text-dark);
}

/* Navigation */
.navbar {
    background-color: var(--primary-color) !important;
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--text-light) !important;
}

.navbar-brand .brand-text {
    font-size: 1.2rem;
    margin-left: 0.5rem;
}

.navbar-brand .trademark {
    font-size: 0.7rem;
    vertical-align: super;
    opacity: 0.8;
}

.nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    margin: 0 0.2rem;
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Cards */
.card {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-hover);
}

.card-header {
    background-color: var(--light-color);
    color: var(--text-dark);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.6rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-dark);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    color: var(--text-dark);
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-hover);
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.dashboard-card .card-footer {
    background: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.dashboard-card .card-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

/* Product Cards */
.product-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-hover);
    background: rgba(255, 255, 255, 1);
}

.product-card .card-body {
    padding: 1.5rem;
}

.product-card .btn {
    width: 100%;
    margin-top: 1rem;
}

/* POS Styles */
.pos-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.cart-item {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: all 0.3s ease;
}

.cart-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.cart-total {
    background: var(--primary-color);
    color: var(--text-light);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 1rem 0;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--dark-color);
    color: var(--text-light);
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* Status Badges */
.badge {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.low-stock {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid var(--warning-color);
}

.out-of-stock {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--danger-color);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: #0f5132;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: #664d03;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: #842029;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--text-light);
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer a {
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer a:hover {
    color: var(--info-color);
    text-decoration: underline;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .navbar-brand .brand-text {
        font-size: 1rem;
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .pos-container {
        padding: 1rem;
    }
    
    .cart-total {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand .brand-text {
        display: none;
    }
    
    .dashboard-card {
        padding: 1.5rem;
    }
    
    .dashboard-card h3 {
        font-size: 1.8rem;
    }
    
    .product-card .card-body {
        padding: 1rem;
    }
    
    .btn-group .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}
