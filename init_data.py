from app import app, db, Product, Customer
from datetime import datetime

def init_sample_data():
    """Initialize the database with your frozen foods inventory data"""
    
    with app.app_context():
        # Create all tables
        db.create_all()
        
        # Check if data already exists
        if Product.query.count() > 0:
            print("Database already contains data. Skipping initialization.")
            return
        
        # Your frozen foods inventory data
        frozen_foods_data = [
            {"name": "Beef Burger Patty", "received": 21, "sold": 6, "stock": 15, "price": 25.00},
            {"name": "Big Jumbo Siomai", "received": 22, "sold": 15, "stock": 7, "price": 15.00},
            {"name": "Cheesy Hambutid", "received": 21, "sold": 10, "stock": 11, "price": 20.00},
            {"name": "Chicken Nuggets", "received": 20, "sold": 19, "stock": 1, "price": 18.00},
            {"name": "Chicken Pastil", "received": 22, "sold": 9, "stock": 13, "price": 22.00},
            {"name": "Chicken Teriyaki", "received": 16, "sold": 11, "stock": 5, "price": 24.00},  # 15+1 free
            {"name": "Chicken Tocino", "received": 15, "sold": 8, "stock": 7, "price": 23.00},
            {"name": "Pork Dinakdakan", "received": 22, "sold": 6, "stock": 16, "price": 26.00},
            {"name": "Pork Longadog", "received": 21, "sold": 10, "stock": 11, "price": 19.00},  # 20+1 free
            {"name": "Pork Longanisa", "received": 15, "sold": 15, "stock": 0, "price": 21.00},
            {"name": "Pork Meatballs", "received": 20, "sold": 11, "stock": 9, "price": 17.00},
            {"name": "Pork Tapa", "received": 22, "sold": 16, "stock": 6, "price": 25.00},  # 20+2 free
            {"name": "Pork Teriyaki", "received": 24, "sold": 9, "stock": 15, "price": 24.00},  # 20+4 free
            {"name": "Pork Tocino", "received": 20, "sold": 16, "stock": 4, "price": 23.00},
            {"name": "Pork Sisig", "received": 22, "sold": 13, "stock": 9, "price": 26.00},
            {"name": "Salami w/ Cheese", "received": 11, "sold": 11, "stock": 0, "price": 28.00},  # 10+1 free
            {"name": "Skinless Longanisa", "received": 16, "sold": 16, "stock": 0, "price": 20.00},  # 15+1 free
            {"name": "Sliced Ham", "received": 10, "sold": 5, "stock": 5, "price": 30.00},
        ]
        
        print("Initializing frozen foods inventory...")
        
        for item_data in frozen_foods_data:
            product = Product(
                name=item_data["name"],
                price=item_data["price"],
                received=item_data["received"],
                sold=item_data["sold"],
                stock=item_data["stock"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.session.add(product)
            print(f"Added: {item_data['name']} - Stock: {item_data['stock']}")
        
        db.session.commit()
        print(f"\nSuccessfully initialized {len(frozen_foods_data)} products!")

        # Add sample customers
        sample_customers = [
            {"name": "Juan Dela Cruz", "phone": "0917-123-4567", "address": "123 Main St, Quezon City"},
            {"name": "Maria Santos", "phone": "0918-234-5678", "address": "456 Oak Ave, Manila"},
            {"name": "Pedro Garcia", "phone": "0919-345-6789", "address": "789 Pine Rd, Makati"},
            {"name": "Ana Reyes", "phone": "0920-456-7890", "address": "321 Elm St, Pasig"},
            {"name": "Jose Mendoza", "phone": "0921-567-8901", "address": "654 Maple Dr, Taguig"},
        ]

        print("\nAdding sample customers...")
        for customer_data in sample_customers:
            customer = Customer(
                name=customer_data["name"],
                phone=customer_data["phone"],
                address=customer_data["address"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.session.add(customer)
            print(f"Added customer: {customer_data['name']}")

        db.session.commit()
        print(f"\nSuccessfully initialized {len(sample_customers)} sample customers!")
        print("Database is ready for use.")

if __name__ == "__main__":
    init_sample_data()
