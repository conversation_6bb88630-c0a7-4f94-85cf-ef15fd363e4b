
from app import app, db, Product, Customer
from datetime import datetime, timezone

def init_sample_data():
    """Initialize the database with your frozen foods inventory data"""
    
    with app.app_context():
        # Create all tables
        db.create_all()
        
        # Check if data already exists
        if Product.query.count() > 0:
            print("Database already contains data. Skipping initialization.")
            return
        
        # Your frozen foods inventory data - Updated with latest stock levels
        frozen_foods_data = [
            {"name": "Beef Burger Patty", "received": 21, "sold": 6, "stock": 15, "price": 80},
            {"name": "Big Siomai", "received": 22, "sold": 15, "stock": 7, "price": 70},
            {"name": "Cheesy Hamonado", "received": 21, "sold": 10, "stock": 11, "price": 90},
            {"name": "Chicken Nuggets", "received": 20, "sold": 19, "stock": 1, "price": 60},
            {"name": "Chicken Pastil", "received": 22, "sold": 9, "stock": 13, "price": 110},
            {"name": "Chicken Teriyaki", "received": 16, "sold": 11, "stock": 5, "price": 80},
            {"name": "Chicken Tocino", "received": 15, "sold": 8, "stock": 7, "price": 80},
            {"name": "Pork Dinakdakan", "received": 22, "sold": 6, "stock": 16, "price": 110},
            {"name": "Pork Longadog", "received": 21, "sold": 10, "stock": 11, "price": 65},
            {"name": "Pork <PERSON>anisa", "received": 15, "sold": 15, "stock": 0, "price": 65},
            {"name": "Pork Meatballs", "received": 20, "sold": 11, "stock": 9, "price": 60},
            {"name": "Pork Tapa", "received": 22, "sold": 16, "stock": 6, "price": 75},
            {"name": "Pork Teriyaki", "received": 24, "sold": 9, "stock": 15, "price": 75},
            {"name": "Pork Tocino", "received": 20, "sold": 16, "stock": 4, "price": 75},
            {"name": "Pork Sisig", "received": 22, "sold": 13, "stock": 9, "price": 110},
            {"name": "Salami w/ Cheese", "received": 11, "sold": 11, "stock": 0, "price": 90},
            {"name": "Skinless Longanisa", "received": 16, "sold": 16, "stock": 0, "price": 65},
            {"name": "Sliced Ham", "received": 10, "sold": 5, "stock": 5, "price": 75}
            ]
        
        print("Initializing frozen foods inventory...")
        
        for item_data in frozen_foods_data:
            product = Product(
                name=item_data["name"],
                price=item_data["price"],
                received=item_data["received"],
                sold=item_data["sold"],
                stock=item_data["stock"],
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(product)
            print(f"Added: {item_data['name']} - Stock: {item_data['stock']}")
        
        db.session.commit()
        print(f"\nSuccessfully initialized {len(frozen_foods_data)} products!")

        # Add sample customers
        sample_customers = [
            {"name": "Juan Dela Cruz", "phone": "0917-123-4567", "address": "123 Main St, Quezon City"},
            {"name": "Maria Santos", "phone": "0918-234-5678", "address": "456 Oak Ave, Manila"},
            {"name": "Pedro Garcia", "phone": "0919-345-6789", "address": "789 Pine Rd, Makati"},
            {"name": "Ana Reyes", "phone": "0920-456-7890", "address": "321 Elm St, Pasig"},
            {"name": "Jose Mendoza", "phone": "0921-567-8901", "address": "654 Maple Dr, Taguig"},
        ]

        print("\nAdding sample customers...")
        for customer_data in sample_customers:
            customer = Customer(
                name=customer_data["name"],
                phone=customer_data["phone"],
                address=customer_data["address"],
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(customer)
            print(f"Added customer: {customer_data['name']}")

        db.session.commit()
        print(f"\nSuccessfully initialized {len(sample_customers)} sample customers!")
        print("Database is ready for use.")

if __name__ == "__main__":
    init_sample_data()

