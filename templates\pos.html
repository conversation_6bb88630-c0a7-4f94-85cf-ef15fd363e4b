{% extends "base.html" %}

{% block title %}Point of Sale - Frozen Foods POS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cash-register"></i> Point of Sale
        </h1>
    </div>
</div>

<div class="row">
    <!-- Products Section -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box"></i> Available Products
                </h5>
            </div>
            <div class="card-body">
                {% if products %}
                <div class="row">
                    {% for product in products %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card product-card h-100" data-product-id="{{ product.id }}"
                             data-product-name="{{ product.name }}"
                             data-product-price="{{ product.price }}"
                             data-product-stock="{{ product.stock }}">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text">
                                    <strong class="text-success">₱{{ "%.2f"|format(product.price) }}</strong><br>
                                    <small class="text-muted">Stock: <span id="stock-{{ product.id }}">{{ product.stock }}</span></small>
                                </p>
                                <div class="quantity-controls mb-2">
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-outline-secondary qty-minus" type="button" data-product-id="{{ product.id }}">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center qty-input"
                                               id="qty-{{ product.id }}" value="1" min="1" max="{{ product.stock }}"
                                               data-product-id="{{ product.id }}">
                                        <button class="btn btn-outline-secondary qty-plus" type="button" data-product-id="{{ product.id }}">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <!-- Quick quantity buttons -->
                                    <div class="quick-qty mt-1">
                                        <button class="btn btn-outline-primary btn-sm quick-qty-btn"
                                                data-product-id="{{ product.id }}" data-qty="5"
                                                {% if product.stock < 5 %}disabled{% endif %}>5</button>
                                        <button class="btn btn-outline-primary btn-sm quick-qty-btn"
                                                data-product-id="{{ product.id }}" data-qty="10"
                                                {% if product.stock < 10 %}disabled{% endif %}>10</button>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm add-to-cart w-100"
                                        data-product-id="{{ product.id }}"
                                        {% if product.stock == 0 %}disabled{% endif %}>
                                    {% if product.stock == 0 %}
                                        <i class="fas fa-times"></i> Out of Stock
                                    {% else %}
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No products available</h4>
                    <p class="text-muted">Add products to your inventory to start selling.</p>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Products
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Cart Section -->
    <div class="col-md-4">
        <div class="card pos-container">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart"></i> Shopping Cart
                </h5>
            </div>
            <div class="card-body">
                <!-- Customer Selection -->
                <div class="mb-3">
                    <label for="customer-select" class="form-label">Customer (Optional)</label>
                    <select class="form-select" id="customer-select">
                        <option value="">Cash Sale</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}">{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                    <div class="form-text">Select customer for credit sales (pautang)</div>
                </div>

                <!-- Payment Method -->
                <div class="mb-3">
                    <label class="form-label">Payment Method</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="payment-method" id="cash-payment" value="cash" checked>
                        <label class="btn btn-outline-success" for="cash-payment">
                            <i class="fas fa-money-bill"></i> Cash
                        </label>

                        <input type="radio" class="btn-check" name="payment-method" id="credit-payment" value="credit">
                        <label class="btn btn-outline-warning" for="credit-payment">
                            <i class="fas fa-credit-card"></i> Credit
                        </label>
                    </div>
                </div>

                <!-- Cash Payment Details -->
                <div id="cash-payment-details" class="mb-3">
                    <label for="cash-received" class="form-label">Cash Received (₱)</label>
                    <input type="number" class="form-control" id="cash-received"
                           step="0.01" min="0" placeholder="0.00">
                    <div class="form-text">Enter amount received from customer</div>

                    <!-- Change Display -->
                    <div id="change-display" class="mt-2" style="display: none;">
                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between">
                                <span><strong>Total:</strong></span>
                                <span id="change-total">₱0.00</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong>Cash Received:</strong></span>
                                <span id="change-received">₱0.00</span>
                            </div>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between">
                                <span><strong>Change:</strong></span>
                                <span id="change-amount" class="text-success fw-bold">₱0.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="cart-items">
                    <div class="text-center text-muted py-4" id="empty-cart">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <p>Cart is empty</p>
                    </div>
                </div>

                <hr>

                <div class="cart-total">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Total:</span>
                        <span id="cart-total">₱0.00</span>
                    </div>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <button class="btn btn-success btn-lg" id="process-sale" disabled>
                        <i class="fas fa-credit-card"></i> <span id="process-btn-text">Process Sale</span>
                    </button>
                    <button class="btn btn-outline-secondary" id="clear-cart" disabled>
                        <i class="fas fa-trash"></i> Clear Cart
                    </button>
                    <button class="btn btn-outline-info btn-sm" id="mobile-help-btn" data-bs-toggle="collapse" data-bs-target="#mobile-help">
                        <i class="fas fa-info-circle"></i> Help
                    </button>
                </div>

                <!-- Mobile Help -->
                <div class="collapse mt-2" id="mobile-help">
                    <div class="card card-body">
                        <h6><i class="fas fa-mobile-alt"></i> How to Use</h6>
                        <small>
                            <strong>1.</strong> Set quantity with +/- buttons<br>
                            <strong>2.</strong> Tap "Add to Cart"<br>
                            <strong>3.</strong> Choose payment method<br>
                            <strong>4.</strong> Select customer (for credit)<br>
                            <strong>5.</strong> Tap "Process Sale"
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Cart Summary Floating Button -->
<div class="mobile-cart-fab d-md-none" id="mobile-cart-fab">
    <button class="btn btn-primary rounded-circle" data-bs-toggle="offcanvas" data-bs-target="#mobile-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-count badge bg-danger" id="mobile-cart-count">0</span>
    </button>
</div>

<!-- Mobile Cart Offcanvas -->
<div class="offcanvas offcanvas-bottom" tabindex="-1" id="mobile-cart">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">Shopping Cart</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body" id="mobile-cart-content">
        <!-- Cart content will be synced here -->
    </div>
</div>

<!-- Sale Success Modal -->
<div class="modal fade" id="saleSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Sale Completed
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <h4>Transaction Successful!</h4>
                <p class="mb-1">Transaction Number: <strong id="transaction-number"></strong></p>
                <p class="mb-3">Total Amount: <strong id="transaction-total" class="text-success"></strong></p>
                <p class="text-muted">Thank you for your purchase!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-plus"></i> New Sale
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let cart = [];

document.addEventListener('DOMContentLoaded', function() {
    // Add to cart functionality
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            const card = this.closest('.product-card');
            const productName = card.dataset.productName;
            const productPrice = parseFloat(card.dataset.productPrice);
            const productStock = parseInt(card.dataset.productStock);
            const quantity = parseInt(document.getElementById(`qty-${productId}`).value);

            addToCart(productId, productName, productPrice, productStock, quantity);
        });
    });

    // Quantity controls
    document.querySelectorAll('.qty-plus').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            const qtyInput = document.getElementById(`qty-${productId}`);
            const currentQty = parseInt(qtyInput.value);
            const maxStock = parseInt(qtyInput.max);

            if (currentQty < maxStock) {
                qtyInput.value = currentQty + 1;
            }
        });
    });

    document.querySelectorAll('.qty-minus').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            const qtyInput = document.getElementById(`qty-${productId}`);
            const currentQty = parseInt(qtyInput.value);

            if (currentQty > 1) {
                qtyInput.value = currentQty - 1;
            }
        });
    });

    // Quantity input validation
    document.querySelectorAll('.qty-input').forEach(input => {
        input.addEventListener('change', function() {
            const value = parseInt(this.value);
            const min = parseInt(this.min);
            const max = parseInt(this.max);

            if (value < min) this.value = min;
            if (value > max) this.value = max;
        });
    });

    // Quick quantity buttons
    document.querySelectorAll('.quick-qty-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            const qty = parseInt(this.dataset.qty);
            const qtyInput = document.getElementById(`qty-${productId}`);

            qtyInput.value = qty;

            // Visual feedback
            this.classList.add('btn-primary');
            this.classList.remove('btn-outline-primary');
            setTimeout(() => {
                this.classList.remove('btn-primary');
                this.classList.add('btn-outline-primary');
            }, 500);
        });
    });

    // Process sale
    document.getElementById('process-sale').addEventListener('click', processSale);

    // Clear cart
    document.getElementById('clear-cart').addEventListener('click', clearCart);

    // Payment method change
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => {
        radio.addEventListener('change', updatePaymentMethod);
    });

    // Customer selection change
    document.getElementById('customer-select').addEventListener('change', updateCustomerSelection);

    // Cash received input
    document.getElementById('cash-received').addEventListener('input', calculateChange);

    // Auto-focus on customer select when credit payment is selected
    updatePaymentMethod();
});

function addToCart(productId, productName, productPrice, productStock, quantity = 1) {
    // Validate quantity
    if (quantity <= 0) {
        showErrorNotification('Quantity must be greater than zero');
        return;
    }

    const existingItem = cart.find(item => item.product_id === productId);

    if (existingItem) {
        const newQuantity = existingItem.quantity + quantity;
        if (newQuantity <= productStock) {
            existingItem.quantity = newQuantity;
            showSuccessNotification(`Added ${quantity} more ${productName} to cart`);
        } else {
            const available = productStock - existingItem.quantity;
            showErrorNotification(`Cannot add ${quantity} more items. Only ${available} available.`);
            return;
        }
    } else {
        if (quantity <= productStock) {
            cart.push({
                product_id: productId,
                name: productName,
                price: productPrice,
                quantity: quantity,
                stock: productStock
            });
            showSuccessNotification(`Added ${quantity} ${productName} to cart`);
        } else {
            showErrorNotification(`Cannot add ${quantity} items. Only ${productStock} available.`);
            return;
        }
    }

    // Reset quantity input to 1 after adding
    document.getElementById(`qty-${productId}`).value = 1;

    updateCartDisplay();

    // Show success feedback on button
    const button = document.querySelector(`[data-product-id="${productId}"].add-to-cart`);
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> Added!';
    button.classList.add('btn-success');
    button.classList.remove('btn-primary');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-primary');
    }, 1000);
}

function updateCartDisplay() {
    const cartItemsContainer = document.getElementById('cart-items');
    const emptyCart = document.getElementById('empty-cart');
    const cartTotal = document.getElementById('cart-total');
    const processSaleBtn = document.getElementById('process-sale');
    const clearCartBtn = document.getElementById('clear-cart');

    if (cart.length === 0) {
        cartItemsContainer.innerHTML = '<div class="text-center text-muted py-4" id="empty-cart"><i class="fas fa-shopping-cart fa-2x mb-2"></i><p>Cart is empty</p></div>';
        cartTotal.textContent = '₱0.00';
        processSaleBtn.disabled = true;
        clearCartBtn.disabled = true;
        return;
    }

    let html = '';
    let total = 0;
    let totalItems = 0;

    cart.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        totalItems += item.quantity;

        html += `
            <div class="cart-item mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-primary">${item.name}</div>
                        <div class="text-muted small">₱${item.price.toFixed(2)} each</div>
                    </div>
                    <div class="text-end">
                        <button class="btn btn-outline-danger btn-sm" onclick="removeFromCart(${index})" title="Remove item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, -1)" title="Decrease quantity">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="btn btn-outline-secondary disabled">${item.quantity}</span>
                        <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, 1)" title="Increase quantity">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="fw-bold text-success">₱${itemTotal.toFixed(2)}</div>
                </div>
            </div>
        `;
    });

    // Add summary
    html += `
        <div class="cart-summary mt-3 p-2 bg-light rounded">
            <div class="d-flex justify-content-between">
                <span>Total Items:</span>
                <span class="fw-bold">${totalItems}</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>Subtotal:</span>
                <span class="fw-bold text-success">₱${total.toFixed(2)}</span>
            </div>
        </div>
    `;

    cartItemsContainer.innerHTML = html;
    cartTotal.textContent = `₱${total.toFixed(2)}`;
    processSaleBtn.disabled = false;
    clearCartBtn.disabled = false;

    // Update mobile cart
    updateMobileCart(html, total, totalItems);

    // Update change calculation
    calculateChange();
}

function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
        removeFromCart(index);
    } else if (newQuantity <= item.stock) {
        item.quantity = newQuantity;
        updateCartDisplay();

        // Show visual feedback
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems[index]) {
            cartItems[index].style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                cartItems[index].style.backgroundColor = '';
            }, 500);
        }
    } else {
        alert(`Cannot add more items. Maximum available: ${item.stock}`);

        // Show error feedback
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems[index]) {
            cartItems[index].style.backgroundColor = '#ffe6e6';
            setTimeout(() => {
                cartItems[index].style.backgroundColor = '';
            }, 1000);
        }
    }
}

function removeFromCart(index) {
    const item = cart[index];

    // Confirm removal for items with quantity > 1
    if (item.quantity > 1) {
        if (!confirm(`Remove all ${item.quantity} ${item.name} from cart?`)) {
            return;
        }
    }

    cart.splice(index, 1);
    updateCartDisplay();

    // Show success message
    showToast(`${item.name} removed from cart`, 'success');
}

function clearCart() {
    if (cart.length > 0) {
        if (confirm('Clear all items from cart?')) {
            cart = [];
            updateCartDisplay();
            showToast('Cart cleared', 'info');
        }
    }
}

function updateMobileCart(html, total, totalItems) {
    // Update mobile cart count
    const mobileCartCount = document.getElementById('mobile-cart-count');
    const mobileCartContent = document.getElementById('mobile-cart-content');

    if (totalItems > 0) {
        mobileCartCount.textContent = totalItems;
        mobileCartCount.classList.remove('hidden');

        // Add mobile-specific cart content
        const mobileHtml = html + `
            <div class="mobile-cart-actions mt-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Total: ₱${total.toFixed(2)}</h5>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-lg" onclick="processSale()" ${cart.length === 0 ? 'disabled' : ''}>
                        <i class="fas fa-credit-card"></i> Process Sale
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearCart()">
                        <i class="fas fa-trash"></i> Clear Cart
                    </button>
                </div>
            </div>
        `;
        mobileCartContent.innerHTML = mobileHtml;
    } else {
        mobileCartCount.classList.add('hidden');
        mobileCartContent.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Cart is empty</h5>
                <p class="text-muted">Add products to start shopping</p>
            </div>
        `;
    }
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function showErrorNotification(message) {
    // Create error notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

function showSuccessNotification(message) {
    // Create success notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i> <strong>Success:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function showWarningNotification(message) {
    // Create warning notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-exclamation-circle"></i> <strong>Warning:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 4000);
}

function updatePaymentMethod() {
    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;
    const customerSelect = document.getElementById('customer-select');
    const processBtnText = document.getElementById('process-btn-text');
    const cashPaymentDetails = document.getElementById('cash-payment-details');

    if (paymentMethod === 'credit') {
        customerSelect.required = true;
        customerSelect.parentElement.classList.add('border', 'border-warning', 'rounded', 'p-2');
        processBtnText.textContent = 'Process Credit Sale';
        cashPaymentDetails.style.display = 'none';

        if (!customerSelect.value) {
            alert('Please select a customer for credit sales');
        }
    } else {
        customerSelect.required = false;
        customerSelect.parentElement.classList.remove('border', 'border-warning', 'rounded', 'p-2');
        processBtnText.textContent = 'Process Cash Sale';
        cashPaymentDetails.style.display = 'block';
        calculateChange();
    }
}

function calculateChange() {
    const cashReceived = parseFloat(document.getElementById('cash-received').value) || 0;
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const change = cashReceived - total;

    const changeDisplay = document.getElementById('change-display');
    const changeTotalEl = document.getElementById('change-total');
    const changeReceivedEl = document.getElementById('change-received');
    const changeAmountEl = document.getElementById('change-amount');

    if (cashReceived > 0) {
        changeDisplay.style.display = 'block';
        changeTotalEl.textContent = `₱${total.toFixed(2)}`;
        changeReceivedEl.textContent = `₱${cashReceived.toFixed(2)}`;
        changeAmountEl.textContent = `₱${change.toFixed(2)}`;

        // Color coding for change
        if (change < 0) {
            changeAmountEl.className = 'text-danger fw-bold';
            changeAmountEl.textContent = `₱${Math.abs(change).toFixed(2)} (Insufficient)`;
        } else {
            changeAmountEl.className = 'text-success fw-bold';
        }
    } else {
        changeDisplay.style.display = 'none';
    }
}

function updateCustomerSelection() {
    const customerSelect = document.getElementById('customer-select');
    const creditRadio = document.getElementById('credit-payment');

    if (customerSelect.value && !creditRadio.checked) {
        // Auto-select credit payment when customer is selected
        creditRadio.checked = true;
        updatePaymentMethod();
    }
}

function processSale() {
    // Validate cart is not empty
    if (cart.length === 0) {
        showErrorNotification('Cart is empty. Please add products before processing sale.');
        return;
    }

    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;
    const customerId = document.getElementById('customer-select').value;
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // Validate total amount
    if (total <= 0) {
        showErrorNotification('Invalid total amount. Please check cart items.');
        return;
    }

    // Validate credit sale requirements
    if (paymentMethod === 'credit' && !customerId) {
        showErrorNotification('Please select a customer for credit sales');
        document.getElementById('customer-select').focus();
        return;
    }

    // Validate cash payment
    if (paymentMethod === 'cash') {
        const cashReceivedInput = document.getElementById('cash-received');
        const cashReceived = parseFloat(cashReceivedInput.value) || 0;

        // Check if cash received field is empty
        if (!cashReceivedInput.value || cashReceivedInput.value.trim() === '') {
            showErrorNotification('Please enter the cash amount received from customer');
            cashReceivedInput.focus();
            return;
        }

        // Check if cash received is sufficient
        if (cashReceived <= 0) {
            showErrorNotification('Cash received must be greater than zero');
            cashReceivedInput.focus();
            return;
        }

        if (cashReceived < total) {
            showErrorNotification(`Insufficient cash received. Total: ₱${total.toFixed(2)}, Received: ₱${cashReceived.toFixed(2)}`);
            cashReceivedInput.focus();
            return;
        }
    }

    const processSaleBtn = document.getElementById('process-sale');
    processSaleBtn.disabled = true;
    processSaleBtn.innerHTML = '<span class="loading"></span> Processing...';

    fetch('/process_sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_items: cart,
            customer_id: customerId || null,
            payment_method: paymentMethod
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            document.getElementById('transaction-number').textContent = data.transaction_number;
            document.getElementById('transaction-total').textContent = `₱${data.total_amount.toFixed(2)}`;

            // Update modal content based on payment method
            const modalTitle = document.querySelector('#saleSuccessModal .modal-title');
            const modalBody = document.querySelector('#saleSuccessModal .modal-body');

            if (data.payment_method === 'credit') {
                modalTitle.innerHTML = '<i class="fas fa-credit-card"></i> Credit Sale Completed';
                modalBody.innerHTML = `
                    <h4>Credit Sale Successful!</h4>
                    <p class="mb-1">Transaction Number: <strong>${data.transaction_number}</strong></p>
                    <p class="mb-1">Customer: <strong>${data.customer_name}</strong></p>
                    <p class="mb-3">Amount: <strong class="text-warning">₱${data.total_amount.toFixed(2)}</strong></p>
                    <p class="text-muted">Amount added to customer's credit balance.</p>
                `;
            } else {
                const cashReceived = parseFloat(document.getElementById('cash-received').value) || 0;
                const change = cashReceived - data.total_amount;

                modalTitle.innerHTML = '<i class="fas fa-check-circle"></i> Cash Sale Completed';
                modalBody.innerHTML = `
                    <h4>Transaction Successful!</h4>
                    <p class="mb-1">Transaction Number: <strong>${data.transaction_number}</strong></p>
                    <p class="mb-1">Total Amount: <strong class="text-success">₱${data.total_amount.toFixed(2)}</strong></p>
                    <p class="mb-1">Cash Received: <strong>₱${cashReceived.toFixed(2)}</strong></p>
                    <p class="mb-3">Change: <strong class="text-info">₱${change.toFixed(2)}</strong></p>
                    <p class="text-muted">Thank you for your purchase!</p>
                `;
            }

            const modal = new bootstrap.Modal(document.getElementById('saleSuccessModal'));
            modal.show();

            showSuccessNotification('Sale processed successfully!');

            clearCart();

            // Reset form
            document.getElementById('customer-select').value = '';
            document.getElementById('cash-received').value = '';
            document.getElementById('cash-payment').checked = true;
            updatePaymentMethod();

            // Refresh page to update stock levels
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            showErrorNotification('Error processing sale: ' + data.message);
        }

        // Re-enable button
        processSaleBtn.disabled = false;
        processSaleBtn.innerHTML = '<i class="fas fa-credit-card"></i> <span id="process-btn-text">Process Sale</span>';
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorNotification('Network error. Please check your connection and try again.');

        // Re-enable button
        processSaleBtn.disabled = false;
        processSaleBtn.innerHTML = '<i class="fas fa-credit-card"></i> <span id="process-btn-text">Process Sale</span>';
    });
}
</script>

<style>
.product-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #0d6efd;
}

.cart-item {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
}

.cart-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quantity-controls {
    max-width: 120px;
    margin: 0 auto;
}

.qty-input {
    font-weight: bold;
    text-align: center;
}

.quick-qty {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.quick-qty-btn {
    min-width: 30px;
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: 4px;
}

.cart-summary {
    border-top: 2px solid #0d6efd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cart-total {
    font-size: 1.25rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .product-card .card-body {
        padding: 1.5rem 1rem;
    }

    .quantity-controls {
        max-width: 140px;
    }

    .cart-item {
        padding: 12px;
        margin-bottom: 12px;
    }

    .btn-group-sm .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        min-height: 44px; /* Touch-friendly size */
    }

    .add-to-cart {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        min-height: 48px;
    }

    .qty-minus, .qty-plus {
        min-width: 44px;
        min-height: 44px;
    }

    .qty-input {
        min-height: 44px;
        font-size: 1.1rem;
    }

    .quick-qty-btn {
        min-width: 44px;
        min-height: 36px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .col-md-8, .col-md-4 {
        padding: 0.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .cart-total {
        font-size: 1.3rem;
        padding: 1.5rem;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.2rem;
        min-height: 56px;
    }

    .form-select, .form-control {
        min-height: 48px;
        font-size: 1.1rem;
    }

    .btn-group .btn {
        min-height: 44px;
        padding: 0.5rem 1rem;
    }

    /* Make product grid single column on very small screens */
    .col-md-6.col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Floating Action Button */
.mobile-cart-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.mobile-cart-fab .btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-count.hidden {
    display: none;
}

/* Mobile offcanvas enhancements */
.offcanvas-bottom {
    height: 70vh;
}

.offcanvas-body {
    padding: 1.5rem;
}
</style>
{% endblock %}
