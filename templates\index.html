{% extends "base.html" %} {% block title %}Dashboard - ATE MEG's FROZEN FOODS{%
endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
  </div>
</div>

<div class="row">
  <div class="col-md-3 mb-4">
    <div class="card text-white bg-primary">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">Products</h5>
            <h2 class="mb-0">{{ total_products or 0 }}</h2>
          </div>
          <div class="align-self-center">
            <i class="fas fa-box fa-2x"></i>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <a
          href="{{ url_for('products') }}"
          class="text-white text-decoration-none"
        >
          View Details <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card text-white bg-success">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">Total Sales</h5>
            <h2 class="mb-0">₱{{ "%.2f"|format(total_sales or 0) }}</h2>
          </div>
          <div class="align-self-center">
            <i class="fas fa-peso-sign fa-2x"></i>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <a
          href="{{ url_for('sales_report') }}"
          class="text-white text-decoration-none"
        >
          View Details <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card text-white bg-warning">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">Low Stock</h5>
            <h2 class="mb-0">{{ low_stock_count or 0 }}</h2>
          </div>
          <div class="align-self-center">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <a
          href="{{ url_for('inventory_report') }}"
          class="text-white text-decoration-none"
        >
          View Details <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card text-white bg-info">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h5 class="card-title">Transactions</h5>
            <h2 class="mb-0">{{ total_transactions or 0 }}</h2>
          </div>
          <div class="align-self-center">
            <i class="fas fa-receipt fa-2x"></i>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <a
          href="{{ url_for('transaction_history') }}"
          class="text-white text-decoration-none"
        >
          View Details <i class="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ url_for('pos') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-cash-register"></i> Start New Sale
          </a>
          <a href="{{ url_for('add_product') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New Product
          </a>
          <a href="{{ url_for('inventory_report') }}" class="btn btn-info">
            <i class="fas fa-warehouse"></i> Check Inventory
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-exclamation-triangle"></i> Low Stock Alert
        </h5>
      </div>
      <div class="card-body">
        {% if low_stock_products %}
        <div class="list-group">
          {% for product in low_stock_products[:5] %}
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
          >
            <span>{{ product.name }}</span>
            <span class="badge bg-warning rounded-pill"
              >{{ product.stock }} left</span
            >
          </div>
          {% endfor %}
        </div>
        {% if low_stock_products|length > 5 %}
        <div class="mt-2">
          <small class="text-muted"
            >And {{ low_stock_products|length - 5 }} more...</small
          >
        </div>
        {% endif %} {% else %}
        <p class="text-muted mb-0">All products are well stocked!</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
