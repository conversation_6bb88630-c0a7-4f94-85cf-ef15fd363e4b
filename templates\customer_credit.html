{% extends "base.html" %}

{% block title %}{{ customer.name }} - Credit History - ATE MEG's FROZEN FOODS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user"></i> {{ customer.name }} - Credit History
            </h1>
            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Customers
            </a>
        </div>
    </div>
</div>

<!-- Customer Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <h5>Total Credit</h5>
                <h3>₱{{ "%.2f"|format(customer.total_credit) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <h5>Total Paid</h5>
                <h3>₱{{ "%.2f"|format(customer.total_paid) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white {% if customer.balance > 0 %}bg-warning{% else %}bg-primary{% endif %}">
            <div class="card-body text-center">
                <h5>Balance</h5>
                <h3>₱{{ "%.2f"|format(customer.balance|abs) }}</h3>
                <small>{% if customer.balance > 0 %}Outstanding{% elif customer.balance < 0 %}Overpaid{% else %}Clear{% endif %}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h6>Quick Actions</h6>
                {% if customer.balance > 0 %}
                <button class="btn btn-success btn-sm mb-1" data-bs-toggle="modal" data-bs-target="#paymentModal">
                    <i class="fas fa-money-bill"></i> Record Payment
                </button>
                {% endif %}
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#creditModal">
                    <i class="fas fa-plus"></i> Add Credit
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Credit History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Credit Transaction History
                </h5>
            </div>
            <div class="card-body">
                {% if credit_history %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Running Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set running_balance = 0 %}
                            {% for credit in credit_history|reverse %}
                                {% if credit.transaction_type == 'credit' %}
                                    {% set running_balance = running_balance + credit.amount %}
                                {% else %}
                                    {% set running_balance = running_balance - credit.amount %}
                                {% endif %}
                            {% endfor %}
                            
                            {% for credit in credit_history %}
                            <tr>
                                <td>{{ credit.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if credit.transaction_type == 'credit' %}
                                        <span class="badge bg-danger">Credit</span>
                                    {% else %}
                                        <span class="badge bg-success">Payment</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if credit.transaction_type == 'credit' %}
                                        <span class="text-danger">₱{{ "%.2f"|format(credit.amount) }}</span>
                                    {% else %}
                                        <span class="text-success">₱{{ "%.2f"|format(credit.amount) }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ credit.description or '-' }}</td>
                                <td>
                                    {% if credit.is_paid %}
                                        <span class="badge bg-success">Paid</span>
                                    {% else %}
                                        <span class="badge bg-warning">Unpaid</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if credit.transaction_type == 'credit' %}
                                        {% set running_balance = running_balance - credit.amount %}
                                    {% else %}
                                        {% set running_balance = running_balance + credit.amount %}
                                    {% endif %}
                                    <strong>₱{{ "%.2f"|format(running_balance) }}</strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No credit history</h4>
                    <p class="text-muted">This customer has no credit transactions yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Customer Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Customer Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>Name:</strong> {{ customer.name }}
                    </div>
                    <div class="col-md-4">
                        <strong>Phone:</strong> {{ customer.phone or 'Not provided' }}
                    </div>
                    <div class="col-md-4">
                        <strong>Address:</strong> {{ customer.address or 'Not provided' }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>Customer Since:</strong> {{ customer.created_at.strftime('%Y-%m-%d') }}
                    </div>
                    <div class="col-md-6">
                        <strong>Last Updated:</strong> {{ customer.updated_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Record Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_payment', customer_id=customer.id) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">Payment Amount (₱)</label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" 
                               step="0.01" min="0" max="{{ customer.balance }}" required>
                        <div class="form-text">Outstanding balance: ₱{{ "%.2f"|format(customer.balance) }}</div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="payment_description" name="description" 
                               value="Payment received" placeholder="Payment description">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Record Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Credit Modal -->
<div class="modal fade" id="creditModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Credit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_credit', customer_id=customer.id) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="credit_amount" class="form-label">Credit Amount (₱)</label>
                        <input type="number" class="form-control" id="credit_amount" name="amount" 
                               step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="credit_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="credit_description" name="description" 
                               placeholder="Reason for credit (e.g., Purchase on credit)">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Credit</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
