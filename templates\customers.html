{% extends "base.html" %}

{% block title %}Customer Management - ATE MEG's FROZEN FOODS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-users"></i> Customer Management
            </h1>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Customer
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Customer List</h5>
            </div>
            <div class="card-body">
                {% if customers %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Total Credit</th>
                                <th>Total Paid</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr class="{% if customer.balance > 0 %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.address %}
                                    <br><small class="text-muted">{{ customer.address[:50] }}{% if customer.address|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </td>
                                <td>{{ customer.phone or '-' }}</td>
                                <td class="text-danger">₱{{ "%.2f"|format(customer.total_credit) }}</td>
                                <td class="text-success">₱{{ "%.2f"|format(customer.total_paid) }}</td>
                                <td>
                                    {% if customer.balance > 0 %}
                                        <span class="badge bg-warning text-dark">₱{{ "%.2f"|format(customer.balance) }}</span>
                                    {% elif customer.balance < 0 %}
                                        <span class="badge bg-info">₱{{ "%.2f"|format(customer.balance|abs) }} overpaid</span>
                                    {% else %}
                                        <span class="badge bg-success">₱0.00</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if customer.balance > 0 %}
                                        <span class="badge bg-warning">Has Debt</span>
                                    {% else %}
                                        <span class="badge bg-success">Clear</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('customer_credit', customer_id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-info" title="View Credit History">
                                            <i class="fas fa-credit-card"></i>
                                        </a>
                                        <a href="{{ url_for('edit_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No customers found</h4>
                    <p class="text-muted">Start by adding your first customer.</p>
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Customer
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if customers %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <h5>Total Customers</h5>
                <h3>{{ customers|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <h5>With Outstanding Debt</h5>
                <h3>{{ customers|selectattr('balance', '>', 0)|list|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body text-center">
                <h5>Total Outstanding</h5>
                <h3>₱{{ "%.2f"|format(customers|map(attribute='balance')|select('>', 0)|sum) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <h5>Total Collected</h5>
                <h3>₱{{ "%.2f"|format(customers|sum(attribute='total_paid')) }}</h3>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
