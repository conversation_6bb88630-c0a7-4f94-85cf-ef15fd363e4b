{% extends "base.html" %}

{% block title %}Products - Frozen Foods POS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-box"></i> Products Management
            </h1>
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Product
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Inventory</h5>
            </div>
            <div class="card-body">
                {% if products %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Product Name</th>
                                <th>Price</th>
                                <th>Received</th>
                                <th>Sold</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr class="{% if product.stock == 0 %}out-of-stock{% elif product.stock <= 5 %}low-stock{% endif %}">
                                <td>
                                    <strong>{{ product.name }}</strong>
                                </td>
                                <td>₱{{ "%.2f"|format(product.price) }}</td>
                                <td>{{ product.received }}</td>
                                <td>{{ product.sold }}</td>
                                <td>
                                    <span class="badge {% if product.stock == 0 %}bg-danger{% elif product.stock <= 5 %}bg-warning{% else %}bg-success{% endif %}">
                                        {{ product.stock }}
                                    </span>
                                </td>
                                <td>
                                    {% if product.stock == 0 %}
                                        <span class="badge bg-danger">Out of Stock</span>
                                    {% elif product.stock <= 5 %}
                                        <span class="badge bg-warning">Low Stock</span>
                                    {% else %}
                                        <span class="badge bg-success">In Stock</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_product', id=product.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_product', id=product.id) }}" 
                                           class="btn btn-sm btn-outline-danger" 
                                           onclick="return confirm('Are you sure you want to delete this product?')" 
                                           title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No products found</h4>
                    <p class="text-muted">Start by adding your first product to the inventory.</p>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Product
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if products %}
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title text-primary">Total Products</h5>
                <h2 class="text-primary">{{ products|length }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title text-success">Total Stock Value</h5>
                <h2 class="text-success">₱{{ "%.2f"|format(products|sum(attribute='stock')|float * products|map(attribute='price')|sum / products|length if products else 0) }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title text-warning">Low Stock Items</h5>
                <h2 class="text-warning">{{ products|selectattr('stock', '<=', 5)|list|length }}</h2>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
