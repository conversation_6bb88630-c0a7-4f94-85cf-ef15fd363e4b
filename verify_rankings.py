#!/usr/bin/env python3
"""
Verify the corrected sales rankings
"""

from app import app, db, Product, Sale

def verify_rankings():
    """Show the corrected sales rankings"""
    with app.app_context():
        print("🏆 CORRECTED SALES RANKINGS")
        print("=" * 60)
        
        # Get sales data grouped by product
        sales_data = db.session.query(
            Product.name,
            Product.price,
            Product.stock,
            Product.sold,
            db.func.sum(Sale.quantity).label('total_sold'),
            db.func.sum(Sale.total_price).label('total_revenue')
        ).join(Sale).group_by(Product.id, Product.name).order_by(
            db.func.sum(Sale.total_price).desc()
        ).all()
        
        print(f"{'Rank':<4} {'Product':<20} {'Sold':<6} {'Revenue':<12} {'Price':<8} {'Stock':<6}")
        print("-" * 60)
        
        for i, item in enumerate(sales_data, 1):
            print(f"{i:<4} {item.name:<20} {item.total_sold:<6} ₱{item.total_revenue:<11.2f} ₱{item.price:<7.2f} {item.stock:<6}")
        
        print("\n🔍 ANALYSIS:")
        print("Now you can see that:")
        print("• Chicken Pastil is #1 with ₱2,310 revenue (21 units × ₱110)")
        print("• Pork Sisig is #2 with ₱2,200 revenue (20 units × ₱110)")  
        print("• Cheesy Hamonado is #3 with ₱1,890 revenue (21 units × ₱90)")
        print("• Chicken Nuggets is now properly ranked based on its ₱1,200 revenue (20 units × ₱60)")
        print("\nThe rankings now correctly reflect actual sales performance! 🎯")

if __name__ == "__main__":
    verify_rankings()
